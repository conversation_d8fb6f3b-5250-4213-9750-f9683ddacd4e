'use client';

import Link from 'next/link';

export default function TestsPage() {
  const testPages = [
    {
      title: 'Dancing Script 改进版测试',
      description: '测试改进版的Dancing Script字体加载和PDF生成',
      href: '/test-dancing-script-improved',
      category: '字体测试'
    },
    {
      title: '完整测试套件',
      description: '运行完整的Dancing Script字体测试套件',
      href: '/test-dancing-script-suite',
      category: '综合测试'
    },
    {
      title: '服务器端PDF生成测试',
      description: '测试服务器端PDF生成功能和性能对比',
      href: '/test-server-pdf',
      category: 'PDF生成'
    },
    {
      title: '字体性能测试',
      description: '测试字体缓存和性能优化效果',
      href: '/test-font-performance',
      category: '性能测试'
    },
    {
      title: '字体降级测试',
      description: '测试字体降级和错误处理机制',
      href: '/test-font-fallback',
      category: '错误处理'
    }
  ];

  const categories = [...new Set(testPages.map(page => page.category))];

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">测试中心</h1>
      
      <div className="mb-8">
        <p className="text-gray-600 mb-4">
          这里包含了所有用于验证Dancing Script字体解决方案的测试页面。
          每个测试都针对特定的功能模块，帮助确保系统的稳定性和性能。
        </p>
      </div>

      {categories.map(category => (
        <div key={category} className="mb-8">
          <h2 className="text-2xl font-semibold mb-4 text-blue-600">{category}</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {testPages
              .filter(page => page.category === category)
              .map(page => (
                <Link
                  key={page.href}
                  href={page.href}
                  className="block p-6 bg-white rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all"
                >
                  <h3 className="text-lg font-semibold mb-2">{page.title}</h3>
                  <p className="text-gray-600 text-sm">{page.description}</p>
                </Link>
              ))}
          </div>
        </div>
      ))}

      <div className="mt-12 p-6 bg-blue-50 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">测试说明</h3>
        <ul className="list-disc list-inside space-y-1 text-sm text-gray-700">
          <li>所有测试都在浏览器中运行，无需额外配置</li>
          <li>测试结果会显示在页面上，并可下载详细报告</li>
          <li>建议按顺序运行测试，从基础功能到高级特性</li>
          <li>如果发现问题，请查看浏览器控制台获取详细错误信息</li>
        </ul>
      </div>
    </div>
  );
}
