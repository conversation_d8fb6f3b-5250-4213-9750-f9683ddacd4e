# 故障排除指南

本文档提供了 Certificate Maker 常见问题的解决方案和调试方法。

## 字体相关问题

### 问题：Dancing Script 字体显示为默认字体

**症状:**
- PDF 中的手写字体显示为 Times Roman 或 Helvetica
- 字体失去手写风格效果

**可能原因:**
1. 字体文件路径错误
2. 字体文件损坏或缺失
3. 字体嵌入失败

**解决方案:**

1. **检查字体文件**
```bash
# 验证字体文件是否存在
ls -la public/fonts/Dancing_Script/
```

2. **验证字体文件完整性**
```typescript
// 使用验证脚本
import { DancingScriptTestSuite } from '@/lib/dancing-script-test-suite';

const testSuite = new DancingScriptTestSuite();
const results = await testSuite.runFullTestSuite();
```

3. **检查浏览器控制台**
- 打开开发者工具
- 查看 Console 标签页的错误信息
- 查看 Network 标签页的字体文件加载状态

4. **使用降级策略**
```typescript
// 确保使用降级方法
const result = await fontEmbedder.embedFontWithFallback('Dancing Script', 400);
```

### 问题：字体加载缓慢

**症状:**
- 首次生成 PDF 时间过长
- 字体加载超时

**解决方案:**

1. **启用字体预加载**
```typescript
// 在应用启动时预加载
const fontEmbedder = new UnifiedFontEmbedder(pdfDoc);
await fontEmbedder.preloadProjectFonts();
```

2. **检查缓存状态**
```typescript
const stats = fontEmbedder.getCacheStats();
console.log(`缓存命中率: ${(stats.hitRate * 100).toFixed(1)}%`);
```

3. **使用服务器端生成**
```typescript
// 对于生产环境，考虑服务器端生成
const result = await generatePDFOnServer(template, data);
```

### 问题：字体权重不正确

**症状:**
- Bold 字体显示为 Regular
- 字体权重映射错误

**解决方案:**

1. **验证字体文件**
```bash
# 检查所有权重文件是否存在
ls -la public/fonts/Dancing_Script/DancingScript-*.ttf
```

2. **检查权重映射**
```typescript
// 验证权重配置
const fontPaths = {
  'Dancing Script': {
    400: '/fonts/Dancing_Script/DancingScript-Regular.ttf',
    500: '/fonts/Dancing_Script/DancingScript-Medium.ttf',
    600: '/fonts/Dancing_Script/DancingScript-SemiBold.ttf',
    700: '/fonts/Dancing_Script/DancingScript-Bold.ttf'
  }
};
```

3. **测试权重加载**
```typescript
// 测试各个权重
const weights = [400, 500, 600, 700];
for (const weight of weights) {
  const result = await fontEmbedder.embedFontWithFallback('Dancing Script', weight);
  console.log(`Weight ${weight}:`, result.success ? '✅' : '❌');
}
```

## PDF 生成问题

### 问题：PDF 生成失败

**症状:**
- 生成 PDF 时抛出异常
- PDF 文件损坏或无法打开

**解决方案:**

1. **检查模板数据**
```typescript
// 验证模板和数据的完整性
if (!template || !data.recipientName) {
  throw new Error('模板或数据不完整');
}
```

2. **使用错误处理**
```typescript
try {
  const pdfBytes = await generateCertificatePDF(template, data);
} catch (error) {
  console.error('PDF 生成失败:', error);
  // 使用降级方案
}
```

3. **检查字体嵌入**
```typescript
// 确保字体成功嵌入
const result = await fontEmbedder.embedFontWithFallback(fontFamily, weight);
if (!result.success) {
  console.warn('字体嵌入失败，使用默认字体');
}
```

### 问题：PDF 文件过大

**症状:**
- 生成的 PDF 文件体积过大
- 下载速度慢

**解决方案:**

1. **启用字体子集化**
```typescript
const font = await pdfDoc.embedFont(fontBytes, {
  subset: true, // 启用子集化
  customName: fontName
});
```

2. **优化图片资源**
- 压缩背景图片
- 使用适当的图片格式
- 减少不必要的图形元素

3. **使用服务器端生成**
- 服务器端可以更好地优化文件大小
- 支持更高级的压缩选项

## 性能问题

### 问题：应用响应缓慢

**症状:**
- 页面加载时间长
- 交互响应延迟

**解决方案:**

1. **检查字体缓存**
```typescript
// 清理过期缓存
const cacheManager = FontCacheManager.getInstance();
cacheManager.cleanup();

// 检查缓存大小
const stats = cacheManager.getStats();
if (stats.totalSize > 50 * 1024 * 1024) { // 50MB
  cacheManager.clear();
}
```

2. **优化字体加载**
```typescript
// 使用批量加载
const fontConfigs = [
  { family: 'Dancing Script', weight: 400 },
  { family: 'Dancing Script', weight: 700 }
];
const results = await fontEmbedder.embedFonts(fontConfigs);
```

3. **监控性能指标**
```typescript
// 测量字体加载时间
const start = performance.now();
const result = await fontEmbedder.embedFontWithFallback('Dancing Script', 400);
const loadTime = performance.now() - start;
console.log(`字体加载时间: ${Math.round(loadTime)}ms`);
```

## 网络问题

### 问题：Google Fonts 加载失败

**症状:**
- 网络错误导致字体加载失败
- CORS 错误

**解决方案:**

1. **使用本地字体文件**
```typescript
// 优先使用本地字体
const LOCAL_FONTS = {
  'Dancing Script': {
    400: '/fonts/Dancing_Script/DancingScript-Regular.ttf'
  }
};
```

2. **配置降级策略**
```typescript
// 确保有可靠的降级方案
const fallbackStrategy = {
  family: 'Dancing Script',
  fallbacks: [
    { type: 'local', family: 'Dancing Script' },
    { type: 'standard', standardFont: StandardFonts.TimesRomanItalic }
  ]
};
```

3. **使用服务器端代理**
- 通过服务器端 API 获取 Google Fonts
- 避免客户端 CORS 限制

## 浏览器兼容性问题

### 问题：特定浏览器中功能异常

**症状:**
- 某些浏览器中字体显示异常
- PDF 生成功能不可用

**解决方案:**

1. **检查浏览器支持**
```typescript
// 检查 pdf-lib 支持
if (typeof window !== 'undefined' && window.PDFDocument) {
  // 浏览器支持
} else {
  // 使用服务器端生成
}
```

2. **使用 Polyfills**
```typescript
// 添加必要的 polyfills
import 'core-js/stable';
import 'regenerator-runtime/runtime';
```

3. **提供降级方案**
```typescript
// 对于不支持的浏览器
if (!browserSupported) {
  // 重定向到服务器端生成
  window.location.href = '/api/generate-pdf';
}
```

## 调试工具

### 启用详细日志

```typescript
// 在开发环境中启用详细日志
if (process.env.NODE_ENV === 'development') {
  console.log('Font loading details:', result);
}
```

### 使用测试页面

访问以下测试页面进行诊断：

- `/tests` - 测试中心
- `/test-dancing-script-suite` - 完整测试套件
- `/test-font-performance` - 性能测试
- `/test-font-fallback` - 降级测试

### 性能分析

```typescript
// 使用 Performance API
const observer = new PerformanceObserver((list) => {
  for (const entry of list.getEntries()) {
    console.log(`${entry.name}: ${entry.duration}ms`);
  }
});
observer.observe({ entryTypes: ['measure'] });

// 标记性能点
performance.mark('font-load-start');
await fontEmbedder.embedFontWithFallback('Dancing Script', 400);
performance.mark('font-load-end');
performance.measure('font-load-duration', 'font-load-start', 'font-load-end');
```

## 获取帮助

如果以上解决方案都无法解决问题：

1. **检查 GitHub Issues**
   - 搜索相似问题
   - 查看已知问题列表

2. **创建详细的问题报告**
   - 包含错误信息
   - 提供复现步骤
   - 附上浏览器和环境信息

3. **提供调试信息**
   ```typescript
   // 收集调试信息
   const debugInfo = {
     userAgent: navigator.userAgent,
     cacheStats: fontEmbedder.getCacheStats(),
     testResults: await testSuite.runFullTestSuite()
   };
   console.log('Debug info:', debugInfo);
   ```

4. **联系维护团队**
   - 通过 GitHub Issues
   - 发送邮件到支持邮箱

## 预防措施

1. **定期运行测试**
   - 设置自动化测试
   - 监控关键功能

2. **监控性能指标**
   - 字体加载时间
   - 缓存命中率
   - PDF 生成成功率

3. **保持依赖更新**
   - 定期更新 pdf-lib
   - 更新字体文件
   - 检查安全漏洞

4. **备份重要配置**
   - 字体配置文件
   - 模板定义
   - 降级策略配置
