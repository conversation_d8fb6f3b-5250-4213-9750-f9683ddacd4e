import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { TemplateManager } from '@/lib/template-manager';
import { SEOManager } from '@/lib/seo-manager';
import PageLayout from '@/components/layout/PageLayout';
import CategoryDetailPage from '@/components/certificate/CategoryDetailPage';

interface CategoryDetailPageProps {
  params: {
    category: string;
  };
}

export async function generateMetadata({ params }: CategoryDetailPageProps): Promise<Metadata> {
  const categoryConfig = TemplateManager.getCategoryConfigBySlug(params.category);
  
  if (!categoryConfig) {
    return {
      title: 'Category Not Found',
      description: 'The requested certificate category was not found.'
    };
  }

  return {
    title: categoryConfig.metaTitle,
    description: categoryConfig.metaDescription,
    keywords: categoryConfig.seoKeywords.join(', '),
    openGraph: {
      title: categoryConfig.metaTitle,
      description: categoryConfig.metaDescription,
      url: `https://certificatemaker.com/certificate-templates/${params.category}/`,
      siteName: 'Certificate Maker',
      type: 'website',
      images: [
        {
          url: `https://certificatemaker.com/og-${params.category}.jpg`,
          width: 1200,
          height: 630,
          alt: categoryConfig.displayName
        }
      ]
    },
    twitter: {
      card: 'summary_large_image',
      title: categoryConfig.metaTitle,
      description: categoryConfig.metaDescription,
      images: [`https://certificatemaker.com/og-${params.category}.jpg`]
    },
    alternates: {
      canonical: `https://certificatemaker.com/certificate-templates/${params.category}/`,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    }
  };
}

export async function generateStaticParams() {
  const categories = TemplateManager.getAllCategories();
  return categories.map((category) => ({
    category: category.urlSlug,
  }));
}

export default function CategoryDetailPageRoute({ params }: CategoryDetailPageProps) {
  const categoryConfig = TemplateManager.getCategoryConfigBySlug(params.category);
  
  if (!categoryConfig) {
    notFound();
  }

  // 获取该分类的模板并序列化
  const templates = TemplateManager.getTemplatesByCategory(categoryConfig.id);
  const serializedTemplates = JSON.parse(JSON.stringify(templates));
  const serializedCategory = JSON.parse(JSON.stringify(categoryConfig));

  // 获取默认模板
  const defaultTemplate = templates.find(t => t.name === categoryConfig.defaultTemplate) || templates[0];
  const serializedDefaultTemplate = defaultTemplate ? JSON.parse(JSON.stringify(defaultTemplate)) : null;

  return (
    <PageLayout className="bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'CollectionPage',
            name: categoryConfig.displayName,
            description: categoryConfig.metaDescription,
            url: `https://certificatemaker.com/certificate-templates/${params.category}/`,
            mainEntity: {
              '@type': 'ItemList',
              name: `${categoryConfig.displayName} Templates`,
              numberOfItems: templates.length,
              itemListElement: templates.map((template, index) => ({
                '@type': 'ListItem',
                position: index + 1,
                item: {
                  '@type': 'CreativeWork',
                  name: template.displayName,
                  description: template.description,
                  creator: {
                    '@type': 'Organization',
                    name: 'Certificate Maker'
                  }
                }
              }))
            },
            breadcrumb: {
              '@type': 'BreadcrumbList',
              itemListElement: [
                {
                  '@type': 'ListItem',
                  position: 1,
                  name: 'Home',
                  item: 'https://certificatemaker.com/'
                },
                {
                  '@type': 'ListItem',
                  position: 2,
                  name: 'Certificate Templates',
                  item: 'https://certificatemaker.com/certificate-templates/'
                },
                {
                  '@type': 'ListItem',
                  position: 3,
                  name: categoryConfig.displayName,
                  item: `https://certificatemaker.com/certificate-templates/${params.category}/`
                }
              ]
            },
            publisher: {
              '@type': 'Organization',
              name: 'Certificate Maker',
              url: 'https://certificatemaker.com/'
            }
          })
        }}
      />

      <CategoryDetailPage
        category={serializedCategory}
        templates={serializedTemplates}
        defaultTemplate={serializedDefaultTemplate}
      />
    </PageLayout>
  );
}
