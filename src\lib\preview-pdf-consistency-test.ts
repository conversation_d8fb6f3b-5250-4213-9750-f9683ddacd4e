/**
 * 预览与PDF一致性测试工具
 * 用于验证预览组件与PDF生成的一致性
 */

import { CertificateTemplate, CertificateData } from '@/types/certificate';
import { PDFGenerator } from './pdf-generator';

export interface ConsistencyTestResult {
  fontMatching: boolean;
  coordinateAccuracy: boolean;
  scalingConsistency: boolean;
  renderingQuality: boolean;
  overallScore: number;
  issues: string[];
}

/**
 * 测试预览与PDF的一致性
 */
export async function testPreviewPdfConsistency(
  template: CertificateTemplate,
  data: CertificateData
): Promise<ConsistencyTestResult> {
  const issues: string[] = [];
  let score = 0;

  // 1. 字体匹配测试
  const fontMatching = await testFontMatching(template);
  if (fontMatching) {
    score += 25;
  } else {
    issues.push('字体匹配不一致');
  }

  // 2. 坐标精度测试
  const coordinateAccuracy = testCoordinateAccuracy(template);
  if (coordinateAccuracy) {
    score += 25;
  } else {
    issues.push('坐标计算不准确');
  }

  // 3. 缩放一致性测试
  const scalingConsistency = testScalingConsistency(template);
  if (scalingConsistency) {
    score += 25;
  } else {
    issues.push('缩放比例不一致');
  }

  // 4. 渲染质量测试
  const renderingQuality = testRenderingQuality(template);
  if (renderingQuality) {
    score += 25;
  } else {
    issues.push('渲染质量不一致');
  }

  return {
    fontMatching,
    coordinateAccuracy,
    scalingConsistency,
    renderingQuality,
    overallScore: score,
    issues
  };
}

/**
 * 测试字体匹配
 */
async function testFontMatching(template: CertificateTemplate): Promise<boolean> {
  try {
    // 检查模板中使用的字体是否都有对应的加载配置
    const usedFonts = [
      template.layout.name.fontFamily,
      template.layout.details.fontFamily,
      template.layout.date.fontFamily,
      template.layout.signature.fontFamily
    ];

    const supportedFonts = [
      'Dancing Script',
      'Playfair Display',
      'Inter',
      'Crimson Text',
      'Source Sans Pro',
      'Great Vibes'
    ];

    for (const font of usedFonts) {
      const isSupported = supportedFonts.some(supported => 
        font.toLowerCase().includes(supported.toLowerCase())
      );
      if (!isSupported) {
        console.warn(`Unsupported font: ${font}`);
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error('Font matching test failed:', error);
    return false;
  }
}

/**
 * 测试坐标精度
 */
function testCoordinateAccuracy(template: CertificateTemplate): boolean {
  try {
    // 检查坐标是否在合理范围内
    const layouts = [
      template.layout.name,
      template.layout.details,
      template.layout.date,
      template.layout.signature
    ];

    const maxWidth = template.orientation === 'landscape' ? 842 : 595;
    const maxHeight = template.orientation === 'landscape' ? 595 : 842;

    for (const layout of layouts) {
      if (layout.x < 0 || layout.x > maxWidth ||
          layout.y < 0 || layout.y > maxHeight ||
          layout.width <= 0 || layout.height <= 0) {
        console.warn(`Invalid coordinates for layout:`, layout);
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error('Coordinate accuracy test failed:', error);
    return false;
  }
}

/**
 * 测试缩放一致性
 */
function testScalingConsistency(template: CertificateTemplate): boolean {
  try {
    // 计算预览组件的缩放因子
    const pdfWidth = template.orientation === 'landscape' ? 842 : 595;
    const containerDisplayWidth = template.orientation === 'landscape' ? 800 : 448;
    const expectedScaleFactor = containerDisplayWidth / pdfWidth;

    // 检查缩放因子是否合理（0.5-0.8之间）
    if (expectedScaleFactor < 0.3 || expectedScaleFactor > 1.0) {
      console.warn(`Invalid scale factor: ${expectedScaleFactor}`);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Scaling consistency test failed:', error);
    return false;
  }
}

/**
 * 测试渲染质量
 */
function testRenderingQuality(template: CertificateTemplate): boolean {
  try {
    // 检查字体大小是否合理
    const layouts = [
      template.layout.name,
      template.layout.details,
      template.layout.date,
      template.layout.signature
    ];

    for (const layout of layouts) {
      if (layout.fontSize < 8 || layout.fontSize > 72) {
        console.warn(`Invalid font size: ${layout.fontSize}`);
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error('Rendering quality test failed:', error);
    return false;
  }
}

/**
 * 生成一致性测试报告
 */
export function generateConsistencyReport(result: ConsistencyTestResult): string {
  const report = [
    '=== 预览与PDF一致性测试报告 ===',
    `总体评分: ${result.overallScore}/100`,
    '',
    '测试项目:',
    `✓ 字体匹配: ${result.fontMatching ? '通过' : '失败'}`,
    `✓ 坐标精度: ${result.coordinateAccuracy ? '通过' : '失败'}`,
    `✓ 缩放一致性: ${result.scalingConsistency ? '通过' : '失败'}`,
    `✓ 渲染质量: ${result.renderingQuality ? '通过' : '失败'}`,
    ''
  ];

  if (result.issues.length > 0) {
    report.push('发现的问题:');
    result.issues.forEach(issue => {
      report.push(`- ${issue}`);
    });
  } else {
    report.push('✅ 所有测试通过！');
  }

  return report.join('\n');
}

/**
 * 运行完整的一致性测试
 */
export async function runFullConsistencyTest(
  templates: CertificateTemplate[],
  testData: CertificateData
): Promise<void> {
  console.log('开始运行预览与PDF一致性测试...');
  
  for (const template of templates) {
    console.log(`\n测试模板: ${template.displayName}`);
    
    try {
      const result = await testPreviewPdfConsistency(template, testData);
      const report = generateConsistencyReport(result);
      console.log(report);
    } catch (error) {
      console.error(`测试模板 ${template.displayName} 时出错:`, error);
    }
  }
  
  console.log('\n一致性测试完成！');
}
