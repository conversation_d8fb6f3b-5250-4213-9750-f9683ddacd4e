'use client';

import Link from 'next/link';
import Image from 'next/image';
import { CertificateTemplate, TemplateCategory } from '@/types/certificate';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Eye, Palette, Monitor, Smartphone } from 'lucide-react';

interface VisualTemplateCardProps {
  template: CertificateTemplate;
  category?: TemplateCategory;
  showCategory?: boolean;
  isSelected?: boolean;
  onSelect?: (template: CertificateTemplate) => void;
  showSelectButton?: boolean;
  className?: string;
}

export default function VisualTemplateCard({ 
  template, 
  category, 
  showCategory = true,
  isSelected = false,
  onSelect,
  showSelectButton = false,
  className = ''
}: VisualTemplateCardProps) {
  const handleSelect = () => {
    if (onSelect) {
      onSelect(template);
    }
  };

  return (
    <Card 
      className={`group cursor-pointer transition-all duration-300 hover:shadow-lg ${
        isSelected 
          ? 'ring-2 ring-blue-500 shadow-lg' 
          : 'hover:shadow-md'
      } ${className}`}
      onClick={handleSelect}
    >
      <CardContent className="p-0">
        {/* 模板预览图 */}
        <div className="relative overflow-hidden rounded-t-lg bg-gray-100">
          {template.preview || template.backgroundImage ? (
            <div className={`relative w-full ${
              template.orientation === 'landscape' ? 'h-48' : 'h-64'
            } bg-white`}>
              <Image
                src={template.preview || template.backgroundImage || '/templates/default-preview.jpg'}
                alt={`${template.displayName} certificate template preview`}
                fill
                className="object-contain p-4"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                }}
              />
              
              {/* 悬停覆盖层 */}
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  {category ? (
                    <Link href={`/certificate-templates/${category.urlSlug}/`}>
                      <Button size="sm" variant="secondary" className="shadow-lg">
                        <Eye className="w-4 h-4 mr-2" />
                        View & Create
                      </Button>
                    </Link>
                  ) : (
                    <Button size="sm" variant="secondary" className="shadow-lg">
                      <Eye className="w-4 h-4 mr-2" />
                      Preview
                    </Button>
                  )}
                </div>
              </div>
            </div>
          ) : (
            <div 
              className={`w-full ${
                template.orientation === 'landscape' ? 'h-48' : 'h-64'
              } flex items-center justify-center relative`}
              style={{
                backgroundColor: template.style?.colors?.background || '#ffffff',
                borderColor: template.style?.colors?.primary || '#1e40af'
              }}
            >
              <div className="text-center p-4">
                <div 
                  className="text-2xl font-bold mb-2"
                  style={{ color: template.style?.colors?.primary || '#1e40af' }}
                >
                  Certificate
                </div>
                <div 
                  className="text-lg"
                  style={{ color: template.style?.colors?.text || '#374151' }}
                >
                  {template.displayName}
                </div>
              </div>
              
              <div 
                className="absolute inset-2 border-2 rounded"
                style={{ borderColor: template.style?.colors?.primary || '#1e40af' }}
              />
            </div>
          )}
          
          {/* 方向指示器 */}
          <div className="absolute top-2 right-2">
            <Badge variant="secondary" className="text-xs">
              {template.orientation === 'landscape' ? (
                <Monitor className="w-3 h-3 mr-1" />
              ) : (
                <Smartphone className="w-3 h-3 mr-1" />
              )}
              {template.orientation}
            </Badge>
          </div>
          
          {/* 选中指示器 */}
          {isSelected && (
            <div className="absolute top-2 left-2">
              <Badge variant="default" className="text-xs bg-blue-600">
                Selected
              </Badge>
            </div>
          )}
        </div>

        {/* 模板信息 */}
        <div className="p-4">
          <div className="mb-3">
            <h3 className="font-semibold text-lg text-gray-900 mb-1 group-hover:text-blue-600 transition-colors">
              {template.displayName}
            </h3>
            <p className="text-sm text-gray-600 line-clamp-2">
              {template.description}
            </p>
          </div>

          {/* 分类标签 */}
          {showCategory && category && (
            <div className="mb-3">
              <Badge variant="outline" className="text-xs">
                {category.displayName}
              </Badge>
            </div>
          )}

          {/* 标签 */}
          <div className="flex flex-wrap gap-1 mb-4">
            {template.tags.slice(0, 3).map((tag, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
            {template.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{template.tags.length - 3}
              </Badge>
            )}
          </div>

          {/* 操作按钮 */}
          <div className="flex space-x-2">
            {showSelectButton ? (
              <Button 
                variant={isSelected ? "default" : "outline"} 
                size="sm" 
                className="flex-1"
                onClick={(e) => {
                  e.stopPropagation();
                  handleSelect();
                }}
              >
                <Palette className="w-4 h-4 mr-2" />
                {isSelected ? 'Selected' : 'Use Template'}
              </Button>
            ) : category ? (
              <Link href={`/certificate-templates/${category.urlSlug}/`} className="flex-1">
                <Button variant="outline" size="sm" className="w-full">
                  <Eye className="w-4 h-4 mr-2" />
                  View & Create
                </Button>
              </Link>
            ) : null}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// 模板网格组件
interface TemplateGridProps {
  templates: CertificateTemplate[];
  selectedTemplate?: CertificateTemplate | null;
  onTemplateSelect?: (template: CertificateTemplate) => void;
  columns?: 1 | 2 | 3 | 4;
  showSelectButton?: boolean;
  categories?: TemplateCategory[];
}

export function TemplateGrid({ 
  templates, 
  selectedTemplate, 
  onTemplateSelect, 
  columns = 3,
  showSelectButton = true,
  categories = []
}: TemplateGridProps) {
  const gridCols = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
  };

  if (templates.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Palette className="w-8 h-8 text-gray-400" />
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          No Templates Available
        </h3>
        <p className="text-gray-600">
          This category doesn't have any templates yet. Please check back later.
        </p>
      </div>
    );
  }

  return (
    <div className={`grid ${gridCols[columns]} gap-6`}>
      {templates.map((template) => {
        const category = categories.find(cat => cat.id === template.category);
        return (
          <VisualTemplateCard
            key={template.id}
            template={template}
            category={category}
            isSelected={selectedTemplate?.id === template.id}
            onSelect={onTemplateSelect}
            showSelectButton={showSelectButton}
          />
        );
      })}
    </div>
  );
}
