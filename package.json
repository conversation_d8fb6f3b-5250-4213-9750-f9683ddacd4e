{"name": "certificate-maker", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "test:templates": "node -e \"require('./src/lib/template-test.ts')\"", "test:performance": "node -e \"require('./src/lib/template-test.ts').runPerformanceTests()\"", "analyze": "cross-env ANALYZE=true next build", "clean": "rimraf .next out dist", "prepare": "husky install", "pre-commit": "lint-staged", "audit:security": "yarn audit --level moderate", "audit:deps": "npx audit-ci --moderate", "health-check": "node scripts/check-project.js"}, "dependencies": {"@hookform/resolvers": "^3.7.0", "@pdf-lib/fontkit": "^1.1.1", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-toast": "^1.1.5", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "lucide-react": "^0.400.0", "next": "14.2.5", "pdf-lib": "^1.17.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.52.1", "tailwind-merge": "^2.4.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8"}, "devDependencies": {"@next/bundle-analyzer": "^14.2.5", "@types/node": "^20.14.10", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.16.0", "@typescript-eslint/parser": "^7.16.0", "audit-ci": "^7.0.1", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-config-next": "14.2.5", "husky": "^9.0.11", "lint-staged": "^15.2.7", "postcss": "^8.4.39", "prettier": "^3.3.2", "prettier-plugin-tailwindcss": "^0.6.5", "rimraf": "^5.0.7", "tailwindcss": "^3.4.6", "typescript": "^5.5.3", "web-vitals": "^4.2.1"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}}