import { NextRequest, NextResponse } from 'next/server';
import { PDFDocument } from 'pdf-lib';
import fontkit from '@pdf-lib/fontkit';
import { promises as fs } from 'fs';
import path from 'path';
import { rgb } from 'pdf-lib';
import { CertificateTemplate, CertificateData } from '@/types/certificate';
import { TemplateManager } from '@/lib/template-manager';

/**
 * 服务器端PDF生成API
 * 解决Dancing Script字体在客户端的显示问题
 */

interface GeneratePDFRequest {
  templateId: string;
  data: CertificateData;
}

// 服务器端字体文件路径
const FONT_PATHS = {
  'Dancing Script': {
    400: path.join(process.cwd(), 'public/fonts/Dancing_Script/DancingScript-Regular.ttf'),
    500: path.join(process.cwd(), 'public/fonts/Dancing_Script/DancingScript-Medium.ttf'),
    600: path.join(process.cwd(), 'public/fonts/Dancing_Script/DancingScript-SemiBold.ttf'),
    700: path.join(process.cwd(), 'public/fonts/Dancing_Script/DancingScript-Bold.ttf')
  },
  'Open Sans': {
    400: path.join(process.cwd(), 'public/fonts/open-sans/open-sans-400.ttf'),
    700: path.join(process.cwd(), 'public/fonts/open-sans/open-sans-700.ttf')
  },
  'Roboto': {
    400: path.join(process.cwd(), 'public/fonts/roboto/roboto-400.ttf'),
    700: path.join(process.cwd(), 'public/fonts/roboto/roboto-700.ttf')
  }
};

/**
 * 服务器端字体嵌入器
 */
class ServerFontEmbedder {
  private pdfDoc: PDFDocument;
  private fontCache = new Map<string, any>();

  constructor(pdfDoc: PDFDocument) {
    this.pdfDoc = pdfDoc;
  }

  async embedFont(family: string, weight: number = 400) {
    const cacheKey = `${family}-${weight}`;
    
    if (this.fontCache.has(cacheKey)) {
      return this.fontCache.get(cacheKey);
    }

    try {
      // 尝试加载本地字体文件
      const fontPath = (FONT_PATHS as any)[family]?.[weight];
      if (fontPath) {
        console.log(`📁 Loading server font: ${fontPath}`);
        
        const fontBytes = await fs.readFile(fontPath);
        const font = await this.pdfDoc.embedFont(fontBytes, {
          subset: true,
          customName: `${family.replace(/\s+/g, '')}-${weight}`
        });
        
        this.fontCache.set(cacheKey, font);
        console.log(`✅ Server font embedded: ${family} ${weight}`);
        return font;
      }

      // 后备到标准字体
      console.log(`⚠️ No server font file for ${family} ${weight}, using fallback`);
      const fallbackFont = await this.pdfDoc.embedStandardFont('Helvetica');
      this.fontCache.set(cacheKey, fallbackFont);
      return fallbackFont;

    } catch (error) {
      console.error(`❌ Error embedding server font ${family} ${weight}:`, error);
      const fallbackFont = await this.pdfDoc.embedStandardFont('Helvetica');
      this.fontCache.set(cacheKey, fallbackFont);
      return fallbackFont;
    }
  }
}

/**
 * 服务器端PDF生成器
 */
class ServerPDFGenerator {
  private template: CertificateTemplate;
  private data: CertificateData;
  private pdfDoc!: PDFDocument;
  private fontEmbedder!: ServerFontEmbedder;

  constructor(template: CertificateTemplate, data: CertificateData) {
    this.template = template;
    this.data = data;
  }

  async generate(): Promise<Uint8Array> {
    console.log('🚀 Starting server-side PDF generation...');
    
    // 创建PDF文档
    this.pdfDoc = await PDFDocument.create();
    this.pdfDoc.registerFontkit(fontkit);
    
    // 初始化字体嵌入器
    this.fontEmbedder = new ServerFontEmbedder(this.pdfDoc);
    
    // 创建页面
    const [width, height] = this.getPageSize();
    const page = this.pdfDoc.addPage([width, height]);
    
    // 绘制背景
    if (this.template.style.background) {
      const bgColor = this.hexToRgb(this.template.style.background);
      page.drawRectangle({
        x: 0,
        y: 0,
        width,
        height,
        color: bgColor
      });
    }
    
    // 绘制边框
    if (this.template.style.border) {
      const borderColor = this.hexToRgb(this.template.style.colors.border);
      const borderWidth = 3;
      
      page.drawRectangle({
        x: borderWidth / 2,
        y: borderWidth / 2,
        width: width - borderWidth,
        height: height - borderWidth,
        borderColor: borderColor,
        borderWidth: borderWidth
      });
    }
    
    // 绘制文本元素
    await this.drawText(page, 'name', this.data.recipientName, width, height);
    await this.drawText(page, 'details', this.data.details, width, height);
    await this.drawText(page, 'date', this.data.date, width, height);
    await this.drawText(page, 'signature', this.data.signature, width, height);
    
    console.log('✅ Server-side PDF generation completed');
    return await this.pdfDoc.save();
  }

  private async drawText(page: any, type: 'name' | 'details' | 'date' | 'signature', text: string, width: number, height: number) {
    const layout = this.template.layout[type];
    const fontConfig = this.template.style.fonts[type];
    
    if (!text || !layout) return;
    
    // 获取字体
    const font = await this.fontEmbedder.embedFont(fontConfig.family, fontConfig.weight);
    
    // 计算位置
    const x = (layout.x / 100) * width;
    const y = height - (layout.y / 100) * height;
    
    // 绘制文本
    page.drawText(text, {
      x,
      y,
      size: fontConfig.size,
      font,
      color: this.hexToRgb(fontConfig.color)
    });
    
    console.log(`✅ Drew ${type}: ${text} with font ${fontConfig.family}`);
  }

  private getPageSize(): [number, number] {
    return this.template.orientation === 'landscape' ? [842, 595] : [595, 842];
  }

  private hexToRgb(hex: string) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    if (!result) {
      return { r: 0, g: 0, b: 0 };
    }
    return {
      r: parseInt(result[1], 16) / 255,
      g: parseInt(result[2], 16) / 255,
      b: parseInt(result[3], 16) / 255
    };
  }
}

export async function POST(request: NextRequest) {
  try {
    const body: GeneratePDFRequest = await request.json();
    
    console.log('📥 Received PDF generation request:', {
      templateId: body.templateId,
      recipientName: body.data.recipientName
    });
    
    // 获取模板
    const template = TemplateManager.getTemplateById(body.templateId);
    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }
    
    // 生成PDF
    const generator = new ServerPDFGenerator(template, body.data);
    const pdfBytes = await generator.generate();
    
    console.log(`✅ PDF generated successfully: ${pdfBytes.length} bytes`);
    
    // 返回PDF
    return new NextResponse(pdfBytes, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="certificate-${body.data.recipientName}.pdf"`,
        'Content-Length': pdfBytes.length.toString()
      }
    });
    
  } catch (error) {
    console.error('❌ Server PDF generation error:', error);
    
    return NextResponse.json(
      { 
        error: 'PDF generation failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
