'use client';

import { useState } from 'react';
import { PDFDocument } from 'pdf-lib';
import fontkit from '@pdf-lib/fontkit';
import { UnifiedFontEmbedder } from '@/lib/unified-font-embedder';

export default function TestDancingScriptImprovedPage() {
  const [logs, setLogs] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addLog = (message: string) => {
    console.log(message);
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testImprovedFontLoading = async () => {
    setIsLoading(true);
    setLogs([]);
    
    try {
      addLog('🚀 开始改进版Dancing Script字体加载测试...');
      
      // 创建PDF文档
      const pdfDoc = await PDFDocument.create();
      pdfDoc.registerFontkit(fontkit);
      
      addLog('📄 PDF文档创建成功');
      
      // 使用统一字体嵌入器
      const fontEmbedder = new UnifiedFontEmbedder(pdfDoc);
      addLog('🔤 初始化统一字体嵌入器');
      
      // 测试所有权重的Dancing Script字体
      const weights = [400, 500, 600, 700];
      const loadedFonts = new Map();
      
      for (const weight of weights) {
        try {
          addLog(`🔤 测试加载 Dancing Script ${weight}...`);
          
          // 使用统一字体嵌入器加载字体
          const result = await fontEmbedder.embedFont('Dancing Script', weight);
          
          if (result.success) {
            loadedFonts.set(weight, result.font);
            addLog(`✅ Dancing Script ${weight} 加载成功: ${result.fontName}`);
            addLog(`   来源: ${result.source}, 自定义: ${result.isCustom ? '是' : '否'}`);
          } else {
            addLog(`❌ Dancing Script ${weight} 加载失败`);
          }
          
        } catch (error) {
          addLog(`❌ Dancing Script ${weight} 加载异常: ${error instanceof Error ? error.message : '未知错误'}`);
        }
      }
      
      // 创建测试页面
      if (loadedFonts.size > 0) {
        addLog('📝 创建测试PDF页面...');
        
        const page = pdfDoc.addPage([600, 800]);
        let yPosition = 750;
        
        // 绘制标题
        page.drawText('Dancing Script 字体测试 (改进版)', {
          x: 50,
          y: yPosition,
          size: 24,
          font: loadedFonts.get(400) || await pdfDoc.embedStandardFont('Helvetica')
        });
        
        yPosition -= 60;
        
        // 测试每个权重
        for (const [weight, font] of loadedFonts.entries()) {
          const testText = `Dancing Script ${weight} - 手写风格测试 Handwriting Style Test`;
          
          page.drawText(testText, {
            x: 50,
            y: yPosition,
            size: 18,
            font: font
          });
          
          yPosition -= 40;
          addLog(`✅ 绘制 Dancing Script ${weight} 文本`);
        }
        
        // 生成PDF
        const pdfBytes = await pdfDoc.save();
        addLog(`📦 PDF生成成功 (${Math.round(pdfBytes.length / 1024)} KB)`);
        
        // 下载PDF
        const blob = new Blob([pdfBytes], { type: 'application/pdf' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'dancing-script-improved-test.pdf';
        link.click();
        URL.revokeObjectURL(url);
        
        addLog('💾 PDF下载完成');
      }
      
      addLog('🎉 Dancing Script字体测试完成！');
      
    } catch (error) {
      addLog(`❌ 测试失败: ${error instanceof Error ? error.message : '未知错误'}`);
      console.error('Font test error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">Dancing Script 字体测试 (改进版)</h1>
      
      <div className="space-y-4 mb-6">
        <button
          onClick={testImprovedFontLoading}
          disabled={isLoading}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
        >
          {isLoading ? '测试中...' : '测试改进版字体加载和PDF生成'}
        </button>
      </div>
      
      <div className="bg-gray-100 p-4 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">测试日志</h2>
        <div className="space-y-1 max-h-96 overflow-y-auto">
          {logs.map((log, index) => (
            <div key={index} className="text-sm font-mono">
              {log}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
