/**
 * 分析和性能监控工具
 * 包含Google Analytics、Core Web Vitals监控等功能
 */

declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    dataLayer: any[];
  }
}

// Google Analytics配置
export const GA_TRACKING_ID = process.env.NEXT_PUBLIC_GA_ID;

/**
 * 初始化Google Analytics
 */
export function initGA() {
  if (!GA_TRACKING_ID || typeof window === 'undefined') return;

  // 加载gtag脚本
  const script1 = document.createElement('script');
  script1.async = true;
  script1.src = `https://www.googletagmanager.com/gtag/js?id=${GA_TRACKING_ID}`;
  document.head.appendChild(script1);

  // 初始化gtag
  window.dataLayer = window.dataLayer || [];
  window.gtag = function gtag() {
    window.dataLayer.push(arguments);
  };
  window.gtag('js', new Date());
  window.gtag('config', GA_TRACKING_ID, {
    page_title: document.title,
    page_location: window.location.href,
  });
}

/**
 * 发送页面浏览事件
 */
export function trackPageView(url: string, title?: string) {
  if (!GA_TRACKING_ID || typeof window === 'undefined') return;

  window.gtag('config', GA_TRACKING_ID, {
    page_title: title || document.title,
    page_location: url,
  });
}

/**
 * 发送自定义事件
 */
export function trackEvent(
  action: string,
  category: string,
  label?: string,
  value?: number,
  customParameters?: Record<string, any>
) {
  if (!GA_TRACKING_ID || typeof window === 'undefined') return;

  window.gtag('event', action, {
    event_category: category,
    event_label: label,
    value: value,
    ...customParameters,
  });
}

/**
 * Core Web Vitals监控
 */
export function initWebVitals() {
  if (typeof window === 'undefined') return;

  // 动态导入web-vitals库
  import('web-vitals').then((webVitals) => {
    if (webVitals.onCLS) webVitals.onCLS(sendToAnalytics);
    if (webVitals.onFID) webVitals.onFID(sendToAnalytics);
    if (webVitals.onFCP) webVitals.onFCP(sendToAnalytics);
    if (webVitals.onLCP) webVitals.onLCP(sendToAnalytics);
    if (webVitals.onTTFB) webVitals.onTTFB(sendToAnalytics);
  }).catch(console.error);
}

/**
 * 发送Web Vitals数据到Analytics
 */
function sendToAnalytics(metric: any) {
  if (!GA_TRACKING_ID) return;

  const value = Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value);
  
  trackEvent(metric.name, 'Web Vitals', metric.id, value, {
    custom_map: { metric_id: 'dimension1', metric_value: 'metric1' },
    metric_id: metric.id,
    metric_value: value,
    metric_delta: metric.delta,
    metric_rating: metric.rating,
  });
}

/**
 * 证书相关的分析事件
 */
export const certificateAnalytics = {
  /**
   * 跟踪模板选择
   */
  templateSelected: (templateName: string, templateId: string) => {
    trackEvent('template_selected', 'Certificate Creation', templateName, undefined, {
      template_id: templateId,
      template_name: templateName,
    });
  },

  /**
   * 跟踪表单字段填写
   */
  formFieldCompleted: (fieldName: string, characterCount: number) => {
    trackEvent('form_field_completed', 'User Interaction', fieldName, characterCount, {
      field_name: fieldName,
      character_count: characterCount,
    });
  },

  /**
   * 跟踪证书生成开始
   */
  certificateGenerationStarted: (templateId: string, formData: any) => {
    trackEvent('certificate_generation_started', 'Certificate Creation', templateId, undefined, {
      template_id: templateId,
      has_name: !!formData.recipientName,
      has_date: !!formData.date,
      has_signature: !!formData.signature,
      has_details: !!formData.details,
      name_length: formData.recipientName?.length || 0,
      details_length: formData.details?.length || 0,
    });
  },

  /**
   * 跟踪证书生成完成
   */
  certificateGenerated: (templateId: string, generationTime: number, fileSize?: number) => {
    trackEvent('certificate_generated', 'Certificate Creation', templateId, generationTime, {
      template_id: templateId,
      generation_time: generationTime,
      file_size: fileSize,
    });
  },

  /**
   * 跟踪PDF下载
   */
  pdfDownloaded: (templateId: string, fileName: string) => {
    trackEvent('pdf_downloaded', 'Conversion', templateId, undefined, {
      template_id: templateId,
      file_name: fileName,
    });
  },

  /**
   * 跟踪错误
   */
  errorOccurred: (errorType: string, errorMessage: string, context?: string) => {
    trackEvent('error_occurred', 'Error', errorType, undefined, {
      error_type: errorType,
      error_message: errorMessage,
      error_context: context,
    });
  },

  /**
   * 跟踪用户会话时长
   */
  sessionDuration: (duration: number) => {
    trackEvent('session_duration', 'User Engagement', 'session_time', duration, {
      session_duration: duration,
    });
  },

  /**
   * 跟踪移动端使用情况
   */
  mobileUsage: (isMobile: boolean, screenWidth: number, screenHeight: number) => {
    trackEvent('device_info', 'User Environment', isMobile ? 'mobile' : 'desktop', undefined, {
      is_mobile: isMobile,
      screen_width: screenWidth,
      screen_height: screenHeight,
      user_agent: navigator.userAgent,
    });
  },
};

/**
 * 性能监控
 */
export const performanceMonitoring = {
  /**
   * 监控页面加载性能
   */
  trackPageLoad: () => {
    if (typeof window === 'undefined') return;

    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      if (navigation) {
        const loadTime = navigation.loadEventEnd - navigation.fetchStart;
        const domContentLoaded = navigation.domContentLoadedEventEnd - navigation.fetchStart;
        const firstPaint = performance.getEntriesByName('first-paint')[0]?.startTime || 0;
        const firstContentfulPaint = performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0;

        trackEvent('page_load_performance', 'Performance', 'load_time', Math.round(loadTime), {
          load_time: Math.round(loadTime),
          dom_content_loaded: Math.round(domContentLoaded),
          first_paint: Math.round(firstPaint),
          first_contentful_paint: Math.round(firstContentfulPaint),
        });
      }
    });
  },

  /**
   * 监控PDF生成性能
   */
  trackPDFGeneration: (startTime: number, endTime: number, templateId: string) => {
    const generationTime = endTime - startTime;
    
    trackEvent('pdf_generation_performance', 'Performance', templateId, Math.round(generationTime), {
      template_id: templateId,
      generation_time: Math.round(generationTime),
    });
  },

  /**
   * 监控资源加载
   */
  trackResourceLoading: () => {
    if (typeof window === 'undefined') return;

    window.addEventListener('load', () => {
      const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
      
      resources.forEach((resource) => {
        if (resource.name.includes('fonts.googleapis.com') || resource.name.includes('.woff')) {
          const loadTime = resource.responseEnd - resource.fetchStart;
          
          trackEvent('font_load_performance', 'Performance', 'font_load', Math.round(loadTime), {
            resource_url: resource.name,
            load_time: Math.round(loadTime),
            resource_size: resource.transferSize,
          });
        }
      });
    });
  },
};

/**
 * 初始化所有分析功能
 */
export function initAnalytics() {
  if (typeof window === 'undefined') return;

  // 初始化Google Analytics
  initGA();
  
  // 初始化Web Vitals监控
  initWebVitals();
  
  // 初始化性能监控
  performanceMonitoring.trackPageLoad();
  performanceMonitoring.trackResourceLoading();
  
  // 跟踪设备信息
  certificateAnalytics.mobileUsage(
    window.innerWidth < 768,
    window.innerWidth,
    window.innerHeight
  );

  // 跟踪会话开始时间
  const sessionStart = Date.now();
  
  // 在页面卸载时跟踪会话时长
  window.addEventListener('beforeunload', () => {
    const sessionDuration = Date.now() - sessionStart;
    certificateAnalytics.sessionDuration(sessionDuration);
  });
}
