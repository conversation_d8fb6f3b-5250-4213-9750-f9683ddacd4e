import { MetadataRoute } from 'next';
import { TemplateManager } from '@/lib/template-manager';

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = 'https://certificatemaker.com';
  const currentDate = new Date();
  const sitemap: MetadataRoute.Sitemap = [];

  // 主页
  sitemap.push({
    url: baseUrl,
    lastModified: currentDate,
    changeFrequency: 'weekly',
    priority: 1.0,
  });

  // 静态页面
  const staticPages = [
    { path: '/certificate-templates', priority: 0.9 },
    { path: '/templates', priority: 0.8 },
    { path: '/about', priority: 0.6 },
    { path: '/privacy', priority: 0.4 },
    { path: '/terms', priority: 0.4 },
  ];

  staticPages.forEach(page => {
    sitemap.push({
      url: `${baseUrl}${page.path}`,
      lastModified: currentDate,
      changeFrequency: 'monthly',
      priority: page.priority,
    });
  });

  // 使用TemplateManager生成动态sitemap条目
  const dynamicEntries = TemplateManager.generateSitemapEntries();

  dynamicEntries.forEach(entry => {
    sitemap.push({
      url: `${baseUrl}${entry.url}`,
      lastModified: entry.lastModified,
      changeFrequency: entry.changeFrequency,
      priority: entry.priority,
    });
  });

  return sitemap;
}
