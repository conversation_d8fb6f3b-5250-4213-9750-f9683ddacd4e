/**
 * Dancing Script字体测试套件
 * 全面验证字体在各种场景下的正确显示
 */

import { PDFDocument } from 'pdf-lib';
import fontkit from '@pdf-lib/fontkit';
import { UnifiedFontEmbedder } from './unified-font-embedder';
import { CertificateTemplate } from '@/types/certificate';
import { TemplateManager } from './template-manager';

export interface FontTestResult {
  testName: string;
  success: boolean;
  details: string;
  fontName?: string;
  source?: string;
  weight?: number;
  renderTime?: number;
  error?: string;
}

export interface TestSuiteResult {
  totalTests: number;
  passedTests: number;
  failedTests: number;
  results: FontTestResult[];
  summary: string;
}

/**
 * Dancing Script字体测试套件类
 */
export class DancingScriptTestSuite {
  private results: FontTestResult[] = [];

  /**
   * 运行完整测试套件
   */
  async runFullTestSuite(): Promise<TestSuiteResult> {
    console.log('🚀 Starting Dancing Script font test suite...');
    this.results = [];

    // 基础字体加载测试
    await this.testBasicFontLoading();
    
    // 权重测试
    await this.testFontWeights();
    
    // PDF嵌入测试
    await this.testPDFEmbedding();
    
    // 模板兼容性测试
    await this.testTemplateCompatibility();
    
    // 性能测试
    await this.testPerformance();
    
    // 渲染质量测试
    await this.testRenderingQuality();

    return this.generateSummary();
  }

  /**
   * 基础字体加载测试
   */
  private async testBasicFontLoading(): Promise<void> {
    console.log('📝 Testing basic font loading...');

    try {
      const pdfDoc = await PDFDocument.create();
      pdfDoc.registerFontkit(fontkit);
      
      const fontEmbedder = new UnifiedFontEmbedder(pdfDoc);
      
      const startTime = performance.now();
      const result = await fontEmbedder.embedFont('Dancing Script', 400);
      const renderTime = performance.now() - startTime;

      this.results.push({
        testName: 'Basic Font Loading',
        success: result.success,
        details: result.success ? 
          `Successfully loaded Dancing Script Regular (${result.source})` :
          'Failed to load Dancing Script Regular',
        fontName: result.fontName,
        source: result.source,
        weight: 400,
        renderTime,
        error: result.success ? undefined : 'Font loading failed'
      });

    } catch (error) {
      this.results.push({
        testName: 'Basic Font Loading',
        success: false,
        details: 'Exception during font loading',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * 字体权重测试
   */
  private async testFontWeights(): Promise<void> {
    console.log('📝 Testing font weights...');

    const weights = [
      { weight: 400, name: 'Regular' },
      { weight: 500, name: 'Medium' },
      { weight: 600, name: 'SemiBold' },
      { weight: 700, name: 'Bold' }
    ];

    try {
      const pdfDoc = await PDFDocument.create();
      pdfDoc.registerFontkit(fontkit);
      
      const fontEmbedder = new UnifiedFontEmbedder(pdfDoc);

      for (const { weight, name } of weights) {
        try {
          const startTime = performance.now();
          const result = await fontEmbedder.embedFont('Dancing Script', weight);
          const renderTime = performance.now() - startTime;

          this.results.push({
            testName: `Font Weight - ${name}`,
            success: result.success,
            details: result.success ? 
              `Successfully loaded Dancing Script ${name} (${result.source})` :
              `Failed to load Dancing Script ${name}`,
            fontName: result.fontName,
            source: result.source,
            weight,
            renderTime,
            error: result.success ? undefined : 'Font weight loading failed'
          });

        } catch (error) {
          this.results.push({
            testName: `Font Weight - ${name}`,
            success: false,
            details: `Exception loading Dancing Script ${name}`,
            weight,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

    } catch (error) {
      this.results.push({
        testName: 'Font Weights Setup',
        success: false,
        details: 'Failed to setup font weight testing',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * PDF嵌入测试
   */
  private async testPDFEmbedding(): Promise<void> {
    console.log('📝 Testing PDF embedding...');

    try {
      const pdfDoc = await PDFDocument.create();
      pdfDoc.registerFontkit(fontkit);
      
      const fontEmbedder = new UnifiedFontEmbedder(pdfDoc);
      
      // 嵌入字体
      const result = await fontEmbedder.embedFont('Dancing Script', 400);
      
      if (result.success) {
        // 创建页面并绘制文本
        const page = pdfDoc.addPage([600, 400]);
        
        const testText = 'Dancing Script Font Test - 手写风格测试';
        
        page.drawText(testText, {
          x: 50,
          y: 300,
          size: 24,
          font: result.font
        });

        // 生成PDF
        const pdfBytes = await pdfDoc.save();
        
        this.results.push({
          testName: 'PDF Embedding',
          success: true,
          details: `Successfully embedded Dancing Script in PDF (${Math.round(pdfBytes.length / 1024)} KB)`,
          fontName: result.fontName,
          source: result.source
        });

      } else {
        this.results.push({
          testName: 'PDF Embedding',
          success: false,
          details: 'Failed to embed Dancing Script in PDF',
          error: 'Font embedding failed'
        });
      }

    } catch (error) {
      this.results.push({
        testName: 'PDF Embedding',
        success: false,
        details: 'Exception during PDF embedding',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * 模板兼容性测试
   */
  private async testTemplateCompatibility(): Promise<void> {
    console.log('📝 Testing template compatibility...');

    const templatesWithDancingScript = [
      'elegant-template-1',
      'elegant-template-2',
      'completion-template-1'
    ];

    for (const templateId of templatesWithDancingScript) {
      try {
        const template = TemplateManager.getTemplateById(templateId);
        
        if (!template) {
          this.results.push({
            testName: `Template Compatibility - ${templateId}`,
            success: false,
            details: 'Template not found',
            error: 'Template not found'
          });
          continue;
        }

        // 检查模板是否使用Dancing Script
        const usesDancingScript = 
          template.style.fonts.name.family === 'Dancing Script' ||
          template.style.fonts.body.family === 'Dancing Script' ||
          template.style.fonts.signature.family === 'Dancing Script';

        if (usesDancingScript) {
          this.results.push({
            testName: `Template Compatibility - ${template.displayName}`,
            success: true,
            details: `Template correctly configured to use Dancing Script`,
          });
        } else {
          this.results.push({
            testName: `Template Compatibility - ${template.displayName}`,
            success: false,
            details: `Template does not use Dancing Script`,
            error: 'Template configuration issue'
          });
        }

      } catch (error) {
        this.results.push({
          testName: `Template Compatibility - ${templateId}`,
          success: false,
          details: 'Exception during template compatibility test',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  }

  /**
   * 性能测试
   */
  private async testPerformance(): Promise<void> {
    console.log('📝 Testing performance...');

    try {
      const pdfDoc = await PDFDocument.create();
      pdfDoc.registerFontkit(fontkit);
      
      const fontEmbedder = new UnifiedFontEmbedder(pdfDoc);

      // 测试首次加载
      const firstLoadStart = performance.now();
      await fontEmbedder.embedFont('Dancing Script', 400);
      const firstLoadTime = performance.now() - firstLoadStart;

      // 测试缓存加载
      const cachedLoadStart = performance.now();
      await fontEmbedder.embedFont('Dancing Script', 400);
      const cachedLoadTime = performance.now() - cachedLoadStart;

      const speedup = firstLoadTime / cachedLoadTime;

      this.results.push({
        testName: 'Performance - First Load',
        success: firstLoadTime < 5000, // 5秒内完成
        details: `First load completed in ${Math.round(firstLoadTime)}ms`,
        renderTime: firstLoadTime
      });

      this.results.push({
        testName: 'Performance - Cached Load',
        success: cachedLoadTime < 100, // 100ms内完成
        details: `Cached load completed in ${Math.round(cachedLoadTime)}ms (${speedup.toFixed(1)}x speedup)`,
        renderTime: cachedLoadTime
      });

    } catch (error) {
      this.results.push({
        testName: 'Performance Test',
        success: false,
        details: 'Exception during performance testing',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * 渲染质量测试
   */
  private async testRenderingQuality(): Promise<void> {
    console.log('📝 Testing rendering quality...');

    try {
      const pdfDoc = await PDFDocument.create();
      pdfDoc.registerFontkit(fontkit);
      
      const fontEmbedder = new UnifiedFontEmbedder(pdfDoc);
      
      const result = await fontEmbedder.embedFont('Dancing Script', 400);
      
      if (result.success) {
        // 检查字体名称是否包含Dancing Script
        const hasCorrectName = result.fontName && 
          result.fontName.toLowerCase().includes('dancing');

        // 检查是否为自定义字体（非标准字体）
        const isCustomFont = result.isCustom;

        this.results.push({
          testName: 'Rendering Quality - Font Name',
          success: hasCorrectName,
          details: hasCorrectName ? 
            `Font name correctly identifies as Dancing Script: ${result.fontName}` :
            `Font name does not identify as Dancing Script: ${result.fontName}`,
          fontName: result.fontName
        });

        this.results.push({
          testName: 'Rendering Quality - Custom Font',
          success: isCustomFont,
          details: isCustomFont ? 
            'Successfully using custom Dancing Script font' :
            'Falling back to standard font instead of custom Dancing Script',
          source: result.source
        });

      } else {
        this.results.push({
          testName: 'Rendering Quality',
          success: false,
          details: 'Cannot test rendering quality - font loading failed',
          error: 'Font loading failed'
        });
      }

    } catch (error) {
      this.results.push({
        testName: 'Rendering Quality',
        success: false,
        details: 'Exception during rendering quality test',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * 生成测试总结
   */
  private generateSummary(): TestSuiteResult {
    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;

    const summary = `Dancing Script Font Test Suite Results:
- Total Tests: ${totalTests}
- Passed: ${passedTests}
- Failed: ${failedTests}
- Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`;

    console.log(summary);

    return {
      totalTests,
      passedTests,
      failedTests,
      results: this.results,
      summary
    };
  }
}
