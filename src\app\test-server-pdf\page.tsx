'use client';

import { useState } from 'react';
import { CertificateData } from '@/types/certificate';
import { TemplateManager } from '@/lib/template-manager';
import { generatePDFOnServer, downloadServerPDF, comparePDFGeneration } from '@/lib/server-pdf-client';
import { generateCertificatePDF } from '@/lib/pdf-generator';

export default function TestServerPDFPage() {
  const [logs, setLogs] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addLog = (message: string) => {
    console.log(message);
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testServerPDF = async () => {
    setIsLoading(true);
    setLogs([]);
    
    try {
      addLog('🚀 开始服务器端PDF生成测试...');
      
      // 获取使用Dancing Script的模板
      const template = TemplateManager.getTemplateById('elegant-template-1');
      if (!template) {
        addLog('❌ 模板未找到');
        return;
      }
      
      addLog(`✅ 使用模板: ${template.displayName}`);
      
      // 准备测试数据
      const testData: CertificateData = {
        templateId: template.id,
        recipientName: 'Dancing Script Server Test',
        date: '2024-01-15',
        signature: 'Beautiful Server Signature',
        details: 'This certificate verifies the server-side Dancing Script font rendering'
      };
      
      addLog('📝 生成服务器端PDF...');
      
      // 生成服务器端PDF
      const result = await generatePDFOnServer(template, testData);
      
      if (result.success && result.pdfBlob) {
        addLog(`✅ 服务器端PDF生成成功 (${Math.round(result.pdfBlob.size / 1024)} KB)`);
        
        // 下载PDF
        const url = URL.createObjectURL(result.pdfBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'dancing-script-server-test.pdf';
        link.click();
        URL.revokeObjectURL(url);
        
        addLog('💾 服务器端PDF下载完成');
      } else {
        addLog(`❌ 服务器端PDF生成失败: ${result.error}`);
      }
      
    } catch (error) {
      addLog(`❌ 测试失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testClientPDF = async () => {
    setIsLoading(true);
    setLogs([]);
    
    try {
      addLog('🚀 开始客户端PDF生成测试...');
      
      // 获取使用Dancing Script的模板
      const template = TemplateManager.getTemplateById('elegant-template-1');
      if (!template) {
        addLog('❌ 模板未找到');
        return;
      }
      
      addLog(`✅ 使用模板: ${template.displayName}`);
      
      // 准备测试数据
      const testData: CertificateData = {
        templateId: template.id,
        recipientName: 'Dancing Script Client Test',
        date: '2024-01-15',
        signature: 'Beautiful Client Signature',
        details: 'This certificate verifies the client-side Dancing Script font rendering'
      };
      
      addLog('📝 生成客户端PDF...');
      
      // 生成客户端PDF
      await generateCertificatePDF(template, testData);
      
      addLog('✅ 客户端PDF生成完成');
      
    } catch (error) {
      addLog(`❌ 测试失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const compareGeneration = async () => {
    setIsLoading(true);
    setLogs([]);
    
    try {
      addLog('🚀 开始PDF生成方式对比测试...');
      
      // 获取使用Dancing Script的模板
      const template = TemplateManager.getTemplateById('elegant-template-1');
      if (!template) {
        addLog('❌ 模板未找到');
        return;
      }
      
      addLog(`✅ 使用模板: ${template.displayName}`);
      
      // 准备测试数据
      const testData: CertificateData = {
        templateId: template.id,
        recipientName: 'Dancing Script Comparison Test',
        date: '2024-01-15',
        signature: 'Beautiful Comparison Signature',
        details: 'This certificate compares client vs server Dancing Script font rendering'
      };
      
      addLog('📊 开始性能对比...');
      
      // 客户端测试
      addLog('🔄 测试客户端生成...');
      const clientStart = performance.now();
      try {
        await generateCertificatePDF(template, testData);
        const clientTime = performance.now() - clientStart;
        addLog(`✅ 客户端生成完成 (${Math.round(clientTime)}ms)`);
      } catch (error) {
        addLog(`❌ 客户端生成失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
      
      // 服务器端测试
      addLog('🌐 测试服务器端生成...');
      const serverStart = performance.now();
      try {
        const result = await generatePDFOnServer(template, testData);
        const serverTime = performance.now() - serverStart;
        
        if (result.success && result.pdfBlob) {
          addLog(`✅ 服务器端生成完成 (${Math.round(serverTime)}ms, ${Math.round(result.pdfBlob.size / 1024)} KB)`);
          
          // 下载对比PDF
          const url = URL.createObjectURL(result.pdfBlob);
          const link = document.createElement('a');
          link.href = url;
          link.download = 'dancing-script-comparison-server.pdf';
          link.click();
          URL.revokeObjectURL(url);
          
          addLog('💾 服务器端对比PDF下载完成');
        } else {
          addLog(`❌ 服务器端生成失败: ${result.error}`);
        }
      } catch (error) {
        addLog(`❌ 服务器端生成失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
      
      addLog('🎉 PDF生成方式对比测试完成！');
      
    } catch (error) {
      addLog(`❌ 对比测试失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">服务器端PDF生成测试</h1>
      
      <div className="space-y-4 mb-6">
        <button
          onClick={testServerPDF}
          disabled={isLoading}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {isLoading ? '测试中...' : '测试服务器端PDF生成'}
        </button>
        
        <button
          onClick={testClientPDF}
          disabled={isLoading}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50 ml-4"
        >
          {isLoading ? '测试中...' : '测试客户端PDF生成'}
        </button>
        
        <button
          onClick={compareGeneration}
          disabled={isLoading}
          className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50 ml-4"
        >
          {isLoading ? '测试中...' : '对比客户端vs服务器端'}
        </button>
      </div>
      
      <div className="bg-gray-100 p-4 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">测试日志</h2>
        <div className="space-y-1 max-h-96 overflow-y-auto">
          {logs.map((log, index) => (
            <div key={index} className="text-sm font-mono">
              {log}
            </div>
          ))}
        </div>
      </div>
      
      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">服务器端PDF生成的优势</h3>
        <ul className="list-disc list-inside space-y-1 text-sm">
          <li>✅ 完全控制字体文件访问，确保Dancing Script正确渲染</li>
          <li>✅ 不受浏览器CORS限制影响</li>
          <li>✅ 服务器资源更强大，处理速度更快</li>
          <li>✅ 所有用户获得一致的PDF输出</li>
          <li>✅ 支持更复杂的字体特性和渲染</li>
          <li>✅ 减少客户端内存使用和处理负载</li>
        </ul>
      </div>
    </div>
  );
}
