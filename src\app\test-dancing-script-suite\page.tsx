'use client';

import { useState } from 'react';
import { DancingScriptTestSuite, TestSuiteResult, FontTestResult } from '@/lib/dancing-script-test-suite';

export default function TestDancingScriptSuitePage() {
  const [testResults, setTestResults] = useState<TestSuiteResult | null>(null);
  const [isRunning, setIsRunning] = useState(false);
  const [currentTest, setCurrentTest] = useState<string>('');

  const runTestSuite = async () => {
    setIsRunning(true);
    setTestResults(null);
    setCurrentTest('初始化测试套件...');
    
    try {
      const testSuite = new DancingScriptTestSuite();
      
      // 模拟进度更新
      const testSteps = [
        '基础字体加载测试',
        '字体权重测试',
        'PDF嵌入测试',
        '模板兼容性测试',
        '性能测试',
        '渲染质量测试',
        '生成测试报告'
      ];

      for (let i = 0; i < testSteps.length - 1; i++) {
        setCurrentTest(testSteps[i]);
        await new Promise(resolve => setTimeout(resolve, 500)); // 模拟测试时间
      }

      setCurrentTest(testSteps[testSteps.length - 1]);
      const results = await testSuite.runFullTestSuite();
      
      setTestResults(results);
      setCurrentTest('测试完成');
      
    } catch (error) {
      console.error('Test suite failed:', error);
      setCurrentTest(`测试失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsRunning(false);
    }
  };

  const getTestStatusIcon = (result: FontTestResult) => {
    return result.success ? '✅' : '❌';
  };

  const getTestStatusColor = (result: FontTestResult) => {
    return result.success ? 'text-green-600' : 'text-red-600';
  };

  const downloadTestReport = () => {
    if (!testResults) return;

    const report = `Dancing Script Font Test Report
Generated: ${new Date().toLocaleString()}

${testResults.summary}

Detailed Results:
${testResults.results.map(result => `
${result.success ? '✅' : '❌'} ${result.testName}
   Details: ${result.details}
   ${result.fontName ? `Font: ${result.fontName}` : ''}
   ${result.source ? `Source: ${result.source}` : ''}
   ${result.weight ? `Weight: ${result.weight}` : ''}
   ${result.renderTime ? `Time: ${Math.round(result.renderTime)}ms` : ''}
   ${result.error ? `Error: ${result.error}` : ''}
`).join('\n')}
`;

    const blob = new Blob([report], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `dancing-script-test-report-${Date.now()}.txt`;
    link.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">Dancing Script 字体测试套件</h1>
      
      <div className="mb-6">
        <button
          onClick={runTestSuite}
          disabled={isRunning}
          className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isRunning ? '运行测试中...' : '运行完整测试套件'}
        </button>
        
        {testResults && (
          <button
            onClick={downloadTestReport}
            className="px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 ml-4"
          >
            下载测试报告
          </button>
        )}
      </div>

      {isRunning && (
        <div className="mb-6 p-4 bg-blue-50 rounded-lg">
          <div className="flex items-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mr-3"></div>
            <span className="text-blue-700">{currentTest}</span>
          </div>
        </div>
      )}

      {testResults && (
        <div className="space-y-6">
          {/* 测试总结 */}
          <div className="p-6 bg-gray-50 rounded-lg">
            <h2 className="text-2xl font-semibold mb-4">测试总结</h2>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600">{testResults.totalTests}</div>
                <div className="text-sm text-gray-600">总测试数</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600">{testResults.passedTests}</div>
                <div className="text-sm text-gray-600">通过测试</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-red-600">{testResults.failedTests}</div>
                <div className="text-sm text-gray-600">失败测试</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600">
                  {((testResults.passedTests / testResults.totalTests) * 100).toFixed(1)}%
                </div>
                <div className="text-sm text-gray-600">成功率</div>
              </div>
            </div>
          </div>

          {/* 详细测试结果 */}
          <div className="p-6 bg-white rounded-lg border">
            <h2 className="text-2xl font-semibold mb-4">详细测试结果</h2>
            <div className="space-y-4">
              {testResults.results.map((result, index) => (
                <div key={index} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center mb-2">
                        <span className="text-xl mr-2">{getTestStatusIcon(result)}</span>
                        <h3 className={`text-lg font-medium ${getTestStatusColor(result)}`}>
                          {result.testName}
                        </h3>
                      </div>
                      <p className="text-gray-700 mb-2">{result.details}</p>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm text-gray-600">
                        {result.fontName && (
                          <div>
                            <span className="font-medium">字体名称:</span> {result.fontName}
                          </div>
                        )}
                        {result.source && (
                          <div>
                            <span className="font-medium">来源:</span> {result.source}
                          </div>
                        )}
                        {result.weight && (
                          <div>
                            <span className="font-medium">权重:</span> {result.weight}
                          </div>
                        )}
                        {result.renderTime && (
                          <div>
                            <span className="font-medium">耗时:</span> {Math.round(result.renderTime)}ms
                          </div>
                        )}
                      </div>
                      
                      {result.error && (
                        <div className="mt-2 p-2 bg-red-50 rounded text-red-700 text-sm">
                          <span className="font-medium">错误:</span> {result.error}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 测试建议 */}
          <div className="p-6 bg-yellow-50 rounded-lg">
            <h2 className="text-2xl font-semibold mb-4">测试建议</h2>
            <div className="space-y-2 text-sm">
              {testResults.failedTests > 0 && (
                <div className="text-red-700">
                  ⚠️ 发现 {testResults.failedTests} 个失败的测试，建议检查字体文件和配置
                </div>
              )}
              {testResults.passedTests === testResults.totalTests && (
                <div className="text-green-700">
                  🎉 所有测试都通过了！Dancing Script字体配置正确
                </div>
              )}
              <div className="text-gray-700">
                💡 定期运行此测试套件以确保字体系统的稳定性
              </div>
              <div className="text-gray-700">
                📊 关注性能测试结果，确保字体加载速度满足用户体验要求
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">测试套件包含的测试项目</h3>
        <ul className="list-disc list-inside space-y-1 text-sm">
          <li>基础字体加载测试 - 验证Dancing Script字体能否正确加载</li>
          <li>字体权重测试 - 测试Regular、Medium、SemiBold、Bold四种权重</li>
          <li>PDF嵌入测试 - 验证字体能否正确嵌入到PDF文档中</li>
          <li>模板兼容性测试 - 检查使用Dancing Script的证书模板配置</li>
          <li>性能测试 - 测量字体加载时间和缓存效果</li>
          <li>渲染质量测试 - 验证字体名称和自定义字体使用情况</li>
        </ul>
      </div>
    </div>
  );
}
