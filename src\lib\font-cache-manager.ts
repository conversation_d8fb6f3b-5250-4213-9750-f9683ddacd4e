/**
 * 字体缓存管理器
 * 优化字体加载性能，减少重复下载
 */

interface CachedFont {
  data: ArrayBuffer;
  timestamp: number;
  size: number;
  family: string;
  weight: number;
  source: 'local' | 'google';
}

interface CacheStats {
  totalSize: number;
  totalFonts: number;
  hitRate: number;
  hits: number;
  misses: number;
}

/**
 * 字体缓存管理器类
 */
export class FontCacheManager {
  private static instance: FontCacheManager;
  private cache = new Map<string, CachedFont>();
  private maxCacheSize = 50 * 1024 * 1024; // 50MB
  private maxAge = 24 * 60 * 60 * 1000; // 24小时
  private stats = {
    hits: 0,
    misses: 0
  };

  private constructor() {
    // 从localStorage恢复缓存
    this.loadFromStorage();
    
    // 定期清理过期缓存
    setInterval(() => this.cleanup(), 60 * 60 * 1000); // 每小时清理一次
  }

  static getInstance(): FontCacheManager {
    if (!FontCacheManager.instance) {
      FontCacheManager.instance = new FontCacheManager();
    }
    return FontCacheManager.instance;
  }

  /**
   * 获取缓存的字体
   */
  get(family: string, weight: number, source: 'local' | 'google' = 'local'): ArrayBuffer | null {
    const key = this.generateKey(family, weight, source);
    const cached = this.cache.get(key);

    if (!cached) {
      this.stats.misses++;
      console.log(`📊 Font cache miss: ${key}`);
      return null;
    }

    // 检查是否过期
    if (Date.now() - cached.timestamp > this.maxAge) {
      this.cache.delete(key);
      this.stats.misses++;
      console.log(`⏰ Font cache expired: ${key}`);
      return null;
    }

    this.stats.hits++;
    console.log(`✅ Font cache hit: ${key} (${Math.round(cached.size / 1024)} KB)`);
    return cached.data;
  }

  /**
   * 缓存字体
   */
  set(family: string, weight: number, data: ArrayBuffer, source: 'local' | 'google' = 'local'): boolean {
    const key = this.generateKey(family, weight, source);
    
    // 检查缓存大小限制
    if (data.byteLength > this.maxCacheSize / 10) { // 单个字体不超过总缓存的10%
      console.warn(`⚠️ Font too large to cache: ${key} (${Math.round(data.byteLength / 1024)} KB)`);
      return false;
    }

    // 确保有足够空间
    this.ensureSpace(data.byteLength);

    const cached: CachedFont = {
      data: data.slice(), // 创建副本
      timestamp: Date.now(),
      size: data.byteLength,
      family,
      weight,
      source
    };

    this.cache.set(key, cached);
    console.log(`💾 Font cached: ${key} (${Math.round(cached.size / 1024)} KB)`);

    // 保存到localStorage（异步）
    this.saveToStorage();

    return true;
  }

  /**
   * 预加载字体
   */
  async preloadFont(family: string, weight: number, source: 'local' | 'google' = 'local'): Promise<boolean> {
    const key = this.generateKey(family, weight, source);
    
    if (this.cache.has(key)) {
      console.log(`✅ Font already cached: ${key}`);
      return true;
    }

    try {
      console.log(`🔄 Preloading font: ${key}`);
      
      let fontData: ArrayBuffer | null = null;

      if (source === 'local') {
        fontData = await this.loadLocalFont(family, weight);
      } else {
        fontData = await this.loadGoogleFont(family, weight);
      }

      if (fontData) {
        this.set(family, weight, fontData, source);
        return true;
      }

      return false;

    } catch (error) {
      console.error(`❌ Font preload failed: ${key}`, error);
      return false;
    }
  }

  /**
   * 批量预加载字体
   */
  async preloadFonts(fonts: Array<{ family: string; weight: number; source?: 'local' | 'google' }>): Promise<void> {
    console.log(`🚀 Preloading ${fonts.length} fonts...`);
    
    const promises = fonts.map(font => 
      this.preloadFont(font.family, font.weight, font.source || 'local')
    );

    const results = await Promise.allSettled(promises);
    const successful = results.filter(r => r.status === 'fulfilled' && r.value).length;
    
    console.log(`✅ Preloaded ${successful}/${fonts.length} fonts`);
  }

  /**
   * 获取缓存统计
   */
  getStats(): CacheStats {
    const totalSize = Array.from(this.cache.values()).reduce((sum, font) => sum + font.size, 0);
    const totalRequests = this.stats.hits + this.stats.misses;
    const hitRate = totalRequests > 0 ? this.stats.hits / totalRequests : 0;

    return {
      totalSize,
      totalFonts: this.cache.size,
      hitRate,
      hits: this.stats.hits,
      misses: this.stats.misses
    };
  }

  /**
   * 清理过期缓存
   */
  cleanup(): void {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, cached] of this.cache.entries()) {
      if (now - cached.timestamp > this.maxAge) {
        this.cache.delete(key);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      console.log(`🧹 Cleaned ${cleaned} expired fonts from cache`);
      this.saveToStorage();
    }
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.clear();
    this.stats.hits = 0;
    this.stats.misses = 0;
    localStorage.removeItem('font-cache');
    console.log('🗑️ Font cache cleared');
  }

  /**
   * 确保有足够的缓存空间
   */
  private ensureSpace(requiredSize: number): void {
    const currentSize = Array.from(this.cache.values()).reduce((sum, font) => sum + font.size, 0);
    
    if (currentSize + requiredSize <= this.maxCacheSize) {
      return;
    }

    // 按时间戳排序，删除最旧的字体
    const sortedEntries = Array.from(this.cache.entries())
      .sort(([, a], [, b]) => a.timestamp - b.timestamp);

    let freedSize = 0;
    for (const [key, cached] of sortedEntries) {
      this.cache.delete(key);
      freedSize += cached.size;
      
      if (currentSize - freedSize + requiredSize <= this.maxCacheSize) {
        break;
      }
    }

    console.log(`🧹 Freed ${Math.round(freedSize / 1024)} KB from font cache`);
  }

  /**
   * 生成缓存键
   */
  private generateKey(family: string, weight: number, source: string): string {
    return `${family}-${weight}-${source}`;
  }

  /**
   * 加载本地字体
   */
  private async loadLocalFont(family: string, weight: number): Promise<ArrayBuffer | null> {
    const fontPaths: Record<string, Record<number, string>> = {
      'Dancing Script': {
        400: '/fonts/Dancing_Script/DancingScript-Regular.ttf',
        500: '/fonts/Dancing_Script/DancingScript-Medium.ttf',
        600: '/fonts/Dancing_Script/DancingScript-SemiBold.ttf',
        700: '/fonts/Dancing_Script/DancingScript-Bold.ttf'
      }
    };

    const fontPath = fontPaths[family]?.[weight];
    if (!fontPath) {
      return null;
    }

    const response = await fetch(fontPath);
    if (!response.ok) {
      return null;
    }

    return await response.arrayBuffer();
  }

  /**
   * 加载Google字体
   */
  private async loadGoogleFont(family: string, weight: number): Promise<ArrayBuffer | null> {
    try {
      // 获取CSS
      const cssUrl = `https://fonts.googleapis.com/css2?family=${encodeURIComponent(family)}:wght@${weight}&display=swap`;
      const cssResponse = await fetch(cssUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      if (!cssResponse.ok) return null;

      const cssText = await cssResponse.text();
      const urlMatch = cssText.match(/url\(([^)]+\.(?:woff2|woff|ttf))\)/);
      
      if (!urlMatch) return null;

      const fontUrl = urlMatch[1].replace(/['"]/g, '');
      
      // 下载字体
      const fontResponse = await fetch(fontUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      if (!fontResponse.ok) return null;

      return await fontResponse.arrayBuffer();

    } catch (error) {
      console.error('Error loading Google font:', error);
      return null;
    }
  }

  /**
   * 从localStorage加载缓存
   */
  private loadFromStorage(): void {
    try {
      const stored = localStorage.getItem('font-cache');
      if (!stored) return;

      const data = JSON.parse(stored);
      
      for (const [key, cached] of Object.entries(data.fonts || {})) {
        const font = cached as any;
        
        // 检查是否过期
        if (Date.now() - font.timestamp > this.maxAge) {
          continue;
        }

        // 恢复ArrayBuffer
        const arrayBuffer = new ArrayBuffer(font.data.length);
        const view = new Uint8Array(arrayBuffer);
        view.set(font.data);

        this.cache.set(key, {
          ...font,
          data: arrayBuffer
        });
      }

      console.log(`📂 Loaded ${this.cache.size} fonts from storage`);

    } catch (error) {
      console.error('Error loading font cache from storage:', error);
    }
  }

  /**
   * 保存缓存到localStorage
   */
  private saveToStorage(): void {
    try {
      const data = {
        fonts: {} as any,
        timestamp: Date.now()
      };

      for (const [key, cached] of this.cache.entries()) {
        data.fonts[key] = {
          ...cached,
          data: Array.from(new Uint8Array(cached.data)) // 转换为数组以便序列化
        };
      }

      localStorage.setItem('font-cache', JSON.stringify(data));

    } catch (error) {
      console.error('Error saving font cache to storage:', error);
    }
  }
}
