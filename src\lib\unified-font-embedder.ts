/**
 * 统一字体嵌入器
 * 解决Dancing Script字体在PDF中的显示问题
 * 优先级：本地TTF文件 > Google Fonts > 标准字体后备
 */

import { PDFDocument, PDFFont, StandardFonts } from 'pdf-lib';
import { FontCacheManager } from './font-cache-manager';
import { FontFallbackManager, FallbackResult } from './font-fallback-manager';

export interface FontEmbedResult {
  font: PDFFont;
  family: string;
  weight: number;
  isCustom: boolean;
  source: 'local' | 'google' | 'standard';
  fontName: string;
  success: boolean;
}

export interface FontConfig {
  family: string;
  weight: number;
  style?: 'normal' | 'italic';
}

/**
 * 统一字体嵌入器类
 */
export class UnifiedFontEmbedder {
  private pdfDoc: PDFDocument;
  private embeddedFonts = new Map<string, FontEmbedResult>();
  private cacheManager: FontCacheManager;
  private fallbackManager: FontFallbackManager;

  // 本地字体文件映射
  private static LOCAL_FONTS: Record<string, Record<number, string>> = {
    'Dancing Script': {
      400: '/fonts/Dancing_Script/DancingScript-Regular.ttf',
      500: '/fonts/Dancing_Script/DancingScript-Medium.ttf',
      600: '/fonts/Dancing_Script/DancingScript-SemiBold.ttf',
      700: '/fonts/Dancing_Script/DancingScript-Bold.ttf'
    },
    'Open Sans': {
      400: '/fonts/open-sans/open-sans-400.ttf',
      700: '/fonts/open-sans/open-sans-700.ttf'
    },
    'Roboto': {
      400: '/fonts/roboto/roboto-400.ttf',
      700: '/fonts/roboto/roboto-700.ttf'
    }
  };

  // 标准字体后备映射
  private static FALLBACK_FONTS: Record<string, StandardFonts> = {
    'Dancing Script': StandardFonts.TimesRomanItalic, // 手写风格用斜体
    'Great Vibes': StandardFonts.TimesRomanItalic,
    'Playfair Display': StandardFonts.TimesRoman,
    'Inter': StandardFonts.Helvetica,
    'Open Sans': StandardFonts.Helvetica,
    'Roboto': StandardFonts.Helvetica,
    'Source Sans Pro': StandardFonts.Helvetica,
    'Crimson Text': StandardFonts.TimesRoman
  };

  constructor(pdfDoc: PDFDocument) {
    this.pdfDoc = pdfDoc;
    this.cacheManager = FontCacheManager.getInstance();
    this.fallbackManager = new FontFallbackManager(pdfDoc);
  }

  /**
   * 嵌入字体（主要方法）
   */
  async embedFont(family: string, weight: number = 400): Promise<FontEmbedResult> {
    const cacheKey = `${family}-${weight}`;

    // 检查是否已嵌入
    if (this.embeddedFonts.has(cacheKey)) {
      console.log(`✅ Font already embedded: ${cacheKey}`);
      return this.embeddedFonts.get(cacheKey)!;
    }

    console.log(`🔤 Embedding font: ${family} ${weight}`);

    try {
      // 1. 优先尝试本地字体
      const localResult = await this.embedLocalFont(family, weight);
      if (localResult.success) {
        this.embeddedFonts.set(cacheKey, localResult);
        return localResult;
      }

      // 2. 尝试Google Fonts
      const googleResult = await this.embedGoogleFont(family, weight);
      if (googleResult.success) {
        this.embeddedFonts.set(cacheKey, googleResult);
        return googleResult;
      }

      // 3. 使用标准字体后备
      const fallbackResult = await this.embedStandardFont(family, weight);
      this.embeddedFonts.set(cacheKey, fallbackResult);
      return fallbackResult;

    } catch (error) {
      console.error(`❌ Error embedding font ${family} ${weight}:`, error);

      // 最终后备：Helvetica
      const emergencyResult = await this.embedStandardFont('Helvetica', weight);
      this.embeddedFonts.set(cacheKey, emergencyResult);
      return emergencyResult;
    }
  }

  /**
   * 使用降级策略嵌入字体（推荐方法）
   */
  async embedFontWithFallback(family: string, weight: number = 400): Promise<FontEmbedResult> {
    const cacheKey = `${family}-${weight}`;

    // 检查是否已嵌入
    if (this.embeddedFonts.has(cacheKey)) {
      console.log(`✅ Font already embedded: ${cacheKey}`);
      return this.embeddedFonts.get(cacheKey)!;
    }

    console.log(`🔤 Embedding font with fallback strategy: ${family} ${weight}`);

    try {
      const fallbackResult = await this.fallbackManager.loadFontWithFallback(family, weight);

      if (fallbackResult.success && fallbackResult.font) {
        const result: FontEmbedResult = {
          font: fallbackResult.font,
          family,
          weight,
          isCustom: !fallbackResult.usedFallback,
          source: fallbackResult.usedFallback ? 'fallback' : 'local',
          fontName: fallbackResult.font.name,
          success: true
        };

        this.embeddedFonts.set(cacheKey, result);

        if (fallbackResult.usedFallback) {
          console.log(`⚠️ Used fallback for ${family} ${weight}: ${fallbackResult.strategy} (level ${fallbackResult.fallbackLevel})`);
        }

        return result;
      }

      // 如果降级也失败了，返回失败结果
      return this.createFailedResult(family, weight);

    } catch (error) {
      console.error(`❌ Error embedding font with fallback ${family} ${weight}:`, error);
      return this.createFailedResult(family, weight);
    }
  }

  /**
   * 嵌入本地字体
   */
  private async embedLocalFont(family: string, weight: number): Promise<FontEmbedResult> {
    try {
      const fontPath = UnifiedFontEmbedder.LOCAL_FONTS[family]?.[weight];
      if (!fontPath) {
        console.log(`⚠️ No local font file for ${family} ${weight}`);
        return this.createFailedResult(family, weight);
      }

      console.log(`📁 Loading local font: ${fontPath}`);

      // 检查缓存
      let fontData = this.cacheManager.get(family, weight, 'local');

      if (!fontData) {
        const response = await fetch(fontPath);
        if (!response.ok) {
          console.warn(`❌ Failed to fetch local font: ${response.status}`);
          return this.createFailedResult(family, weight);
        }

        fontData = await response.arrayBuffer();

        // 验证字体文件
        if (!this.validateFontData(fontData)) {
          console.warn(`❌ Invalid font data: ${fontPath}`);
          return this.createFailedResult(family, weight);
        }

        // 缓存字体数据
        this.cacheManager.set(family, weight, fontData, 'local');
      }

      // 嵌入字体到PDF
      const font = await this.pdfDoc.embedFont(fontData, {
        subset: true, // 使用子集以减小文件大小
        customName: this.generateFontName(family, weight)
      });

      console.log(`✅ Local font embedded successfully: ${family} ${weight}`);
      console.log(`   Font name: ${font.name}`);

      return {
        font,
        family,
        weight,
        isCustom: true,
        source: 'local',
        fontName: font.name,
        success: true
      };

    } catch (error) {
      console.warn(`⚠️ Local font embedding failed for ${family} ${weight}:`, error);
      return this.createFailedResult(family, weight);
    }
  }

  /**
   * 嵌入Google Fonts字体
   */
  private async embedGoogleFont(family: string, weight: number): Promise<FontEmbedResult> {
    try {
      console.log(`🌐 Loading Google font: ${family} ${weight}`);

      // 生成Google Fonts CSS URL
      const cssUrl = `https://fonts.googleapis.com/css2?family=${encodeURIComponent(family)}:wght@${weight}&display=swap`;
      
      // 获取CSS
      const cssResponse = await fetch(cssUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      if (!cssResponse.ok) {
        console.warn(`❌ Failed to fetch Google Fonts CSS: ${cssResponse.status}`);
        return this.createFailedResult(family, weight);
      }

      const cssText = await cssResponse.text();

      // 提取字体URL
      const urlMatch = cssText.match(/url\(([^)]+\.(?:woff2|woff|ttf))\)/);
      if (!urlMatch) {
        console.warn(`❌ No font URL found in CSS for ${family} ${weight}`);
        return this.createFailedResult(family, weight);
      }

      const fontUrl = urlMatch[1].replace(/['"]/g, '');
      console.log(`🔗 Font URL: ${fontUrl.substring(0, 80)}...`);

      // 检查缓存
      let fontData = this.cacheManager.get(family, weight, 'google');

      if (!fontData) {
        // 下载字体文件
        const fontResponse = await fetch(fontUrl, {
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
          }
        });

        if (!fontResponse.ok) {
          console.warn(`❌ Failed to fetch Google font: ${fontResponse.status}`);
          return this.createFailedResult(family, weight);
        }

        fontData = await fontResponse.arrayBuffer();

        // 验证字体数据
        if (!this.validateFontData(fontData)) {
          console.warn(`❌ Invalid Google font data: ${family} ${weight}`);
          return this.createFailedResult(family, weight);
        }

        // 缓存字体数据
        this.cacheManager.set(family, weight, fontData, 'google');
      }

      // 嵌入字体到PDF
      const font = await this.pdfDoc.embedFont(fontData, {
        subset: true,
        customName: this.generateFontName(family, weight)
      });

      console.log(`✅ Google font embedded successfully: ${family} ${weight}`);
      console.log(`   Font name: ${font.name}`);

      return {
        font,
        family,
        weight,
        isCustom: true,
        source: 'google',
        fontName: font.name,
        success: true
      };

    } catch (error) {
      console.warn(`⚠️ Google font embedding failed for ${family} ${weight}:`, error);
      return this.createFailedResult(family, weight);
    }
  }

  /**
   * 嵌入标准字体
   */
  private async embedStandardFont(family: string, weight: number): Promise<FontEmbedResult> {
    try {
      console.log(`🔄 Using standard font for ${family} ${weight}`);

      const standardFont = UnifiedFontEmbedder.FALLBACK_FONTS[family] || StandardFonts.Helvetica;
      const isBold = weight >= 600;

      // 选择合适的标准字体
      let selectedFont = standardFont;
      if (isBold && standardFont === StandardFonts.Helvetica) {
        selectedFont = StandardFonts.HelveticaBold;
      } else if (isBold && standardFont === StandardFonts.TimesRoman) {
        selectedFont = StandardFonts.TimesRomanBold;
      } else if (isBold && standardFont === StandardFonts.TimesRomanItalic) {
        selectedFont = StandardFonts.TimesRomanBoldItalic;
      }

      const font = await this.pdfDoc.embedFont(selectedFont);

      console.log(`✅ Standard font embedded: ${selectedFont}`);

      return {
        font,
        family: selectedFont,
        weight: isBold ? 700 : 400,
        isCustom: false,
        source: 'standard',
        fontName: font.name,
        success: true
      };

    } catch (error) {
      console.error(`❌ Error embedding standard font:`, error);
      throw error;
    }
  }

  /**
   * 验证字体数据
   */
  private validateFontData(fontData: ArrayBuffer): boolean {
    if (fontData.byteLength < 100) {
      return false;
    }

    const view = new DataView(fontData);
    const signature = view.getUint32(0, false);

    // 检查TTF/OTF签名
    const validSignatures = [
      0x00010000, // TTF
      0x74727565, // 'true'
      0x4F54544F  // 'OTTO'
    ];

    return validSignatures.includes(signature);
  }

  /**
   * 生成字体名称
   */
  private generateFontName(family: string, weight: number): string {
    const weightName = weight >= 700 ? 'Bold' : 
                      weight >= 600 ? 'SemiBold' : 
                      weight >= 500 ? 'Medium' : 'Regular';
    return `${family.replace(/\s+/g, '')}-${weightName}`;
  }

  /**
   * 创建失败结果
   */
  private createFailedResult(family: string, weight: number): FontEmbedResult {
    return {
      font: null as any, // 将在后续处理中替换
      family,
      weight,
      isCustom: false,
      source: 'standard',
      fontName: '',
      success: false
    };
  }

  /**
   * 批量嵌入字体
   */
  async embedFonts(fontConfigs: FontConfig[]): Promise<Map<string, FontEmbedResult>> {
    const results = new Map<string, FontEmbedResult>();

    for (const config of fontConfigs) {
      const result = await this.embedFont(config.family, config.weight);
      const key = `${config.family}-${config.weight}`;
      results.set(key, result);
      results.set(config.family, result); // 也存储简单名称
    }

    return results;
  }

  /**
   * 预加载项目字体
   */
  async preloadProjectFonts(): Promise<void> {
    const projectFonts = [
      { family: 'Dancing Script', weight: 400 },
      { family: 'Dancing Script', weight: 500 },
      { family: 'Dancing Script', weight: 600 },
      { family: 'Dancing Script', weight: 700 },
      { family: 'Open Sans', weight: 400 },
      { family: 'Open Sans', weight: 700 },
      { family: 'Roboto', weight: 400 },
      { family: 'Roboto', weight: 700 }
    ];

    await this.cacheManager.preloadFonts(projectFonts);
  }

  /**
   * 获取缓存统计
   */
  getCacheStats() {
    return this.cacheManager.getStats();
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cacheManager.clear();
    this.embeddedFonts.clear();
  }

  /**
   * 获取嵌入的字体
   */
  getEmbeddedFont(family: string, weight?: number): FontEmbedResult | null {
    const key = weight ? `${family}-${weight}` : family;
    return this.embeddedFonts.get(key) || null;
  }
}
